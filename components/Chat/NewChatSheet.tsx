import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { FriendService } from '~/services/FriendService';
import { UserStore } from '~/store/store';

// Mock data for contacts/friends
const mockFriends = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
  {
    id: 2,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'offline',
  },
  {
    id: 3,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
  {
    id: 4,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'offline',
  },
  {
    id: 5,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
  {
    id: 6,
    name: '<PERSON>',
    avatar: 'https://placeimg.com/140/140/people',
    status: 'online',
  },
];

interface Friend {
  id: number;
  name: string;
  avatar: string;
  status: string;
}

interface NewChatSheetProps {
  bottomSheetRef: React.RefObject<BottomSheet>;
  onSelectFriend: (friend: Friend) => void;
  colors: any;
  isDark: boolean;
}

const NewChatSheet = ({ bottomSheetRef, onSelectFriend, colors, isDark }: NewChatSheetProps) => {
  const user = UserStore((state: any) => state.user);
  const [searchQuery, setSearchQuery] = useState('');
  const [friends, setFriends] = useState(mockFriends);
  const [realFriends, setRealFriends] = useState<Friend[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch friends when component mounts
  useEffect(() => {
    const fetchFriends = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        const response = await FriendService.getFriends(user.id);
        if (response.success && response.body) {
          // Transform the response to match our Friend interface
          const friendsData = response.body.map((friend: any) => ({
            id: friend.id,
            name: friend.fullName || friend.name,
            avatar:
              friend.profilePicture?.[0]?.secureUrl ||
              friend.profilePhoto ||
              'https://via.placeholder.com/150',
            status: friend.isOnline ? 'online' : 'offline',
          }));
          setRealFriends(friendsData);
          setFriends(friendsData);
        }
      } catch (error) {
        console.error('Error fetching friends:', error);
        // Fallback to mock data if API fails
        setFriends(mockFriends);
      } finally {
        setLoading(false);
      }
    };

    fetchFriends();
  }, [user?.id]);

  const handleSearch = (text: string) => {
    setSearchQuery(text);
    const dataToSearch = realFriends.length > 0 ? realFriends : mockFriends;

    if (text.trim() === '') {
      setFriends(dataToSearch);
    } else {
      const filtered = dataToSearch.filter((friend) =>
        friend.name.toLowerCase().includes(text.toLowerCase())
      );
      setFriends(filtered);
    }
  };

  const renderFriend = ({ item }: { item: Friend }) => (
    <TouchableOpacity
      className="flex-row items-center border-b px-4 py-3"
      style={{ borderBottomColor: colors.grey5 }}
      onPress={() => onSelectFriend(item)}>
      <View className="relative">
        <Image source={{ uri: item.avatar }} className="h-12 w-12 rounded-full" />
        <View
          className="absolute bottom-0 right-0 h-3 w-3 rounded-full border-2"
          style={{
            backgroundColor: item.status === 'online' ? '#22c55e' : '#94a3b8',
            borderColor: isDark ? colors.background : colors.root,
          }}
        />
      </View>
      <View className="ml-3">
        <Text className="font-medium text-base" style={{ color: colors.foreground }}>
          {item.name}
        </Text>
        <Text className="text-sm" style={{ color: colors.grey }}>
          {item.status === 'online' ? 'Online' : 'Offline'}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <BottomSheetView style={{ backgroundColor: colors.background, flex: 1 }}>
      <View className="px-4 pb-6 pt-2">
        <Text className="mb-4 text-center font-bold text-xl" style={{ color: colors.foreground }}>
          New Chat
        </Text>

        <View className="mb-4">
          <View
            className="flex-row items-center rounded-xl px-3 py-2.5"
            style={{ backgroundColor: colors.grey5 }}>
            <Ionicons name="search" size={20} color={colors.grey} />
            <TextInput
              className="ml-2 flex-1 text-base"
              placeholder="Search friends..."
              placeholderTextColor={colors.grey}
              value={searchQuery}
              onChangeText={handleSearch}
              style={{ color: colors.foreground }}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Ionicons name="close-circle" size={20} color={colors.grey} />
              </TouchableOpacity>
            ) : null}
          </View>
        </View>

        <Text className="mb-2 text-base font-semibold" style={{ color: colors.grey }}>
          Friends
        </Text>

        {loading ? (
          <View className="flex-1 items-center justify-center py-10">
            <ActivityIndicator size="large" color={colors.primary} />
            <Text className="mt-4 text-center text-base" style={{ color: colors.grey }}>
              Loading friends...
            </Text>
          </View>
        ) : friends.length > 0 ? (
          <FlatList
            data={friends}
            keyExtractor={(item) => item.id.toString()}
            renderItem={renderFriend}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 40 }}
          />
        ) : (
          <View className="flex-1 items-center justify-center py-10">
            <Ionicons name="people" size={48} color={colors.grey} />
            <Text className="mt-4 text-center text-base" style={{ color: colors.grey }}>
              {searchQuery
                ? 'No friends found with this name'
                : 'No friends yet. Add some friends to start chatting!'}
            </Text>
          </View>
        )}
      </View>
    </BottomSheetView>
  );
};

export default NewChatSheet;
