import { useState, useEffect, useCallback } from 'react';
import { FirebaseService, FirebaseMessage } from '~/services/FirebaseService';
import { UserService } from '~/services/UserService';
import { UserStore } from '~/store/store';
import { ChatItem, Message, FirebaseChatUser } from '~/types/chat_type';

export const useFirebaseChat = () => {
  const [chats, setChats] = useState<ChatItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const userData = UserStore((state) => state.user);

  // Convert Firebase message to GiftedChat message format
  const convertFirebaseMessageToGiftedChat = (
    firebaseMessage: FirebaseMessage & { id: string },
    users: { [userId: string]: FirebaseChatUser }
  ): Message => {
    const user = users[firebaseMessage.senderId];
    return {
      _id: firebaseMessage.id,
      text: firebaseMessage.messageText,
      createdAt: new Date(firebaseMessage.messageTime),
      user: {
        _id: firebaseMessage.senderId,
        name: user?.name || 'Unknown User',
        avatar: user?.avatar || 'https://via.placeholder.com/40',
      },
      ...(firebaseMessage.uploadUrl && { image: firebaseMessage.uploadUrl }),
    };
  };

  // Load user's chats
  const loadChats = useCallback(async () => {
    if (!userData?.id) return;

    try {
      setLoading(true);
      setError(null);

      // Get user's chat IDs from Firebase
      const userChats = await FirebaseService.getUserChats(userData.id);
      const chatItems: ChatItem[] = [];

      // For each chat, get the other user's info and last message
      for (const [otherUserId, chatId] of Object.entries(userChats)) {
        try {
          // Get other user's info from your backend
          const otherUserResponse = await UserService.getOtherUser({ userId: otherUserId });
          console.log('other user response', otherUserResponse);
          if (otherUserResponse.success) {
            const otherUser = otherUserResponse.body;

            // Get last message from Firebase
            const lastMessage = await FirebaseService.getLastMessage(chatId);

            chatItems.push({
              id: otherUserId,
              name: otherUser.fullName || otherUser.username || 'Unknown User',
              avatar: otherUser.profilePicture || 'https://via.placeholder.com/40',
              lastMessage: lastMessage?.messageText || 'No messages yet',
              timestamp: lastMessage ? new Date(lastMessage.messageTime).toLocaleTimeString() : '',
              chatId: chatId,
            });
          }
        } catch (error) {
          console.error(`Error loading chat with user ${otherUserId}:`, error);
        }
      }

      // Sort chats by last message time
      chatItems.sort((a, b) => {
        // This is a simple sort, you might want to improve this
        return b.timestamp.localeCompare(a.timestamp);
      });

      setChats(chatItems);
    } catch (error) {
      console.error('Error loading chats:', error);
      setError('Failed to load chats');
    } finally {
      setLoading(false);
    }
  }, [userData?.id]);

  // Start a new chat with a user
  const startChat = useCallback(
    async (otherUserId: string): Promise<string | null> => {
      if (!userData?.id) return null;

      try {
        // Create or get existing chat
        const chatId = await FirebaseService.getOrCreatePrivateChat(userData.id, otherUserId);

        // Reload chats to include the new one
        await loadChats();

        return chatId;
      } catch (error) {
        console.error('Error starting chat:', error);
        setError('Failed to start chat');
        return null;
      }
    },
    [userData?.id, loadChats]
  );

  // Send a message
  const sendMessage = useCallback(
    async (
      chatId: string,
      receiverId: string,
      messageText: string,
      uploadUrl?: string
    ): Promise<boolean> => {
      if (!userData?.id) return false;

      try {
        await FirebaseService.sendMessage(chatId, userData.id, receiverId, messageText, uploadUrl);
        return true;
      } catch (error) {
        console.error('Error sending message:', error);
        setError('Failed to send message');
        return false;
      }
    },
    [userData?.id]
  );

  // Listen to messages in a specific chat
  const listenToMessages = useCallback(
    (chatId: string, callback: (messages: Message[]) => void) => {
      if (!chatId) return () => {};

      return FirebaseService.listenToMessages(chatId, async (firebaseMessages) => {
        try {
          // Get user info for all participants
          const userIds = [...new Set(firebaseMessages.map((msg) => msg.senderId))];
          const users: { [userId: string]: FirebaseChatUser } = {};

          // Load user data for message senders
          for (const userId of userIds) {
            try {
              if (userId === userData?.id) {
                // Current user
                users[userId] = {
                  id: userData.id,
                  name: userData.fullName || userData.username || 'You',
                  avatar: userData.profilePicture || 'https://via.placeholder.com/40',
                  email: userData.email,
                  fullName: userData.fullName,
                };
              } else {
                // Other user
                const userResponse = await UserService.getOtherUser({ userId });
                if (userResponse.success) {
                  const user = userResponse.body;
                  users[userId] = {
                    id: userId,
                    name: user.fullName || user.username || 'Unknown User',
                    avatar: user.profilePicture || 'https://via.placeholder.com/40',
                    email: user.email,
                    fullName: user.fullName,
                  };
                }
              }
            } catch (error) {
              console.error(`Error loading user ${userId}:`, error);
              // Fallback user data
              users[userId] = {
                id: userId,
                name: 'Unknown User',
                avatar: 'https://via.placeholder.com/40',
                email: '',
                fullName: 'Unknown User',
              };
            }
          }

          // Convert Firebase messages to GiftedChat format
          const giftedChatMessages = firebaseMessages.map((msg) =>
            convertFirebaseMessageToGiftedChat(msg, users)
          );

          // Reverse for GiftedChat (newest first)
          callback(giftedChatMessages.reverse());
        } catch (error) {
          console.error('Error processing messages:', error);
          callback([]);
        }
      });
    },
    [userData]
  );

  // Load chats on mount and when user changes
  useEffect(() => {
    if (userData?.id) {
      loadChats();
    }
  }, [userData?.id, loadChats]);

  return {
    chats,
    loading,
    error,
    loadChats,
    startChat,
    sendMessage,
    listenToMessages,
  };
};
