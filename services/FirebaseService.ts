import database from '@react-native-firebase/database';
import { UserStore } from '~/store/store';

export interface FirebaseMessage {
  messageText: string;
  messageTime: number;
  receiverId: string;
  senderId: string;
  uploadUrl?: string;
}

export interface FirebaseChat {
  messages: { [messageId: string]: FirebaseMessage };
}

export interface FirebaseUser {
  email: string;
  fullName: string;
}

export class FirebaseService {
  private static dbRef = database();

  // Initialize Firebase user when they register/login
  static async ensureFirebaseUser(userId: string, fullName: string, email: string) {
    try {
      const userRef = this.dbRef.ref(`users/${userId}`);
      const snapshot = await userRef.once('value');

      if (!snapshot.exists()) {
        // User doesn't exist in Firebase, create them
        await userRef.set({
          email,
          fullName,
        });
      }
      return true;
    } catch (error) {
      console.error('Error ensuring Firebase user:', error);
      throw error;
    }
  }

  // Get or create private chat between two users
  static async getOrCreatePrivateChat(userOneId: string, userTwoId: string): Promise<string> {
    try {
      // Check if chat already exists in userChats
      const userChatsRef = this.dbRef.ref(`userChats/${userOneId}/${userTwoId}`);
      const snapshot = await userChatsRef.once('value');

      if (snapshot.exists()) {
        return snapshot.val();
      }

      // Create new chat
      const chatId = this.dbRef.ref('privateChats').push().key;

      if (!chatId) {
        throw new Error('Failed to generate chat ID');
      }

      // Initialize empty chat
      await this.dbRef.ref(`privateChats/${chatId}`).set({
        messages: {},
      });

      // Update userChats for both users
      await this.dbRef.ref(`userChats/${userOneId}/${userTwoId}`).set(chatId);
      await this.dbRef.ref(`userChats/${userTwoId}/${userOneId}`).set(chatId);

      return chatId;
    } catch (error) {
      console.error('Error getting/creating private chat:', error);
      throw error;
    }
  }

  // Send a message to a chat
  static async sendMessage(
    chatId: string,
    senderId: string,
    receiverId: string,
    messageText: string,
    uploadUrl?: string
  ) {
    try {
      const messageRef = this.dbRef.ref(`privateChats/${chatId}/messages`).push();
      const messageData: FirebaseMessage = {
        messageText,
        messageTime: Date.now(),
        receiverId,
        senderId,
        ...(uploadUrl && { uploadUrl }),
      };

      await messageRef.set(messageData);
      return messageRef.key;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Listen to messages in a chat
  static listenToMessages(chatId: string, callback: (messages: FirebaseMessage[]) => void) {
    const messagesRef = this.dbRef.ref(`privateChats/${chatId}/messages`);

    const listener = messagesRef.on('value', (snapshot) => {
      const messagesData = snapshot.val();
      if (messagesData) {
        const messages = Object.keys(messagesData).map((key) => ({
          id: key,
          ...messagesData[key],
        }));
        // Sort by timestamp
        messages.sort((a, b) => a.messageTime - b.messageTime);
        callback(messages);
      } else {
        callback([]);
      }
    });

    // Return unsubscribe function
    return () => messagesRef.off('value', listener);
  }

  // Get user's chats
  static async getUserChats(userId: string): Promise<{ [otherUserId: string]: string }> {
    try {
      const userChatsRef = this.dbRef.ref(`userChats/${userId}`);
      const snapshot = await userChatsRef.once('value');
      return snapshot.val() || {};
    } catch (error) {
      console.error('Error getting user chats:', error);
      throw error;
    }
  }

  // Get user info from Firebase
  static async getFirebaseUser(userId: string): Promise<FirebaseUser | null> {
    try {
      const userRef = this.dbRef.ref(`users/${userId}`);
      const snapshot = await userRef.once('value');
      return snapshot.exists() ? snapshot.val() : null;
    } catch (error) {
      console.error('Error getting Firebase user:', error);
      throw error;
    }
  }

  // Get last message from a chat
  static async getLastMessage(chatId: string): Promise<FirebaseMessage | null> {
    try {
      const messagesRef = this.dbRef.ref(`privateChats/${chatId}/messages`);
      const snapshot = await messagesRef.orderByChild('messageTime').limitToLast(1).once('value');
      const messagesData = snapshot.val();

      if (messagesData) {
        const messageKey = Object.keys(messagesData)[0];
        return messagesData[messageKey];
      }
      return null;
    } catch (error) {
      console.error('Error getting last message:', error);
      return null;
    }
  }
}
