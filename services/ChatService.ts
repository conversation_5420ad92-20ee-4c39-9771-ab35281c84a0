import * as SecureStore from 'expo-secure-store';
import axiosInstance from './config';

export class ChatService {
  static async createFirebaseUser(userDetails: {
    fullName: string;
    email: string;
    userId: string;
  }) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post('/chat/v1/create-firebase-user', userDetails, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async createPrivateChat(userOneId: string, userTwoId: string) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const response = await axiosInstance.post(
        '/chat/v1/create-private-chat',
        {
          userOneId,
          userTwoId,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }

  static async sendMessage(
    imageFiles: File[],
    chatId: string,
    senderId: string,
    receiverId: string,
    messageText: string
  ) {
    try {
      const token = await SecureStore.getItemAsync('accessToken');
      const formData = new FormData();
      if (imageFiles.length > 0) {
        imageFiles.forEach((file) => formData.append('messageUploads', file));
      }
      formData.append('chatId', chatId);
      formData.append('senderId', senderId);
      formData.append('receiverId', receiverId);
      formData.append('messageText', messageText);

      const response = await axiosInstance.post('/chat/v1/send-message', formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
      });

      const data = response.data;
      if (data.success) {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      throw error;
    }
  }
}
